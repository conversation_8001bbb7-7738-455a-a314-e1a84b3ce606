{"name": "web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack -p 3000", "build": "next build", "start": "next start -p 3000", "typecheck": "tsc --noEmit", "lint": "biome check .", "format": "biome format . --write", "test": "vitest run"}, "dependencies": {"@ai-sdk/google": "2.0.0-beta.15", "@ai-sdk/openai": "^1.3.23", "@ai-sdk/react": "2.0.0-beta.28", "@better-fetch/fetch": "^1.1.18", "@blocknote/core": "0.35.0", "@blocknote/mantine": "0.35.0", "@blocknote/react": "0.35.0", "@blocknote/shadcn": "0.35.0", "@convex-dev/better-auth": "^0.7.13", "convex": "^1.25.4", "@emoji-mart/data": "^1.2.1", "@emoji-mart/react": "^1.1.1", "@hcaptcha/react-hcaptcha": "^1.12.0", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.2.1", "@inngest/agent-kit": "^0.9.0", "@inngest/realtime": "^0.3.1", "@liveblocks/client": "3.2.0", "@liveblocks/core": "3.2.0", "@liveblocks/node": "3.2.0", "@liveblocks/react": "3.2.0", "@liveblocks/react-blocknote": "3.2.0", "@liveblocks/react-ui": "3.2.0", "@marsidev/react-turnstile": "^1.1.0", "@next/swc-wasm-nodejs": "15.4.4", "@polar-sh/better-auth": "1.0.8", "@polar-sh/sdk": "0.34.8", "@tailwindcss/postcss": "^4.1.11", "@tanstack/query-core": "^5.83.1", "@tanstack/react-query": "^5.84.0", "@tanstack/react-query-devtools": "^5.84.0", "@tanstack/react-table": "^8.21.3", "@types/node": "24.1.0", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@uploadthing/react": "^7.3.2", "@upstash/redis": "^1.35.3", "@workspace/backend": "workspace:*", "@workspace/ui": "workspace:*", "@xyflow/react": "^12.8.2", "ai": "5.0.0-beta.28", "better-auth": "^1.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "eslint": "9.32.0", "eslint-config-next": "15.4.4", "exa-js": "^1.8.26", "framer-motion": "^12.23.12", "geist": "^1.4.2", "inngest": "^3.40.1", "loops": "^5.0.1", "lucide-react": "^0.528.0", "moment": "^2.30.1", "next": "15.4.4", "next-themes": "^0.4.6", "openai": "^5.11.0", "postcss": "8.5.6", "react": "19.1.0", "react-colorful": "^5.6.1", "react-day-picker": "^9.8.1", "react-dom": "19.1.0", "react-error-boundary": "^6.0.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.4", "react-textarea-autosize": "^8.5.9", "reactflow": "^11.11.4", "recharts": "^3.1.0", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "resend": "^4.7.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwind-scrollbar-hide": "^4.0.0", "tailwind-variants": "^2.1.0", "tailwindcss": "4.1.11", "tailwindcss-animate": "^1.0.7", "tldraw": "^3.15.0", "ua-parser-js": "^2.0.4", "uploadthing": "^7.7.3", "uuid": "^11.1.0", "zod": "3.25.76"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "@workspace/typescript-config": "workspace:*", "typescript": "^5.8.3"}}