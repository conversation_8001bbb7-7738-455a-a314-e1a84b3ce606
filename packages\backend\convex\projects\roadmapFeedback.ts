import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { betterAuth } from "../auth";
import { RoadmapFeedbackSentiment } from "../../schema/enum";

export const getRoadmapFeedbackByRoadmapItem = query({
  args: {
    roadmapItemId: v.id("RoadmapItem"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    return await ctx.db
      .query("RoadmapFeedback")
      .filter((q) => q.eq(q.field("roadmapItemId"), args.roadmapItemId))
      .collect();
  },
});

export const createRoadmapFeedback = mutation({
  args: {
    roadmapItemId: v.id("RoadmapItem"),
    userId: v.optional(v.string()),
    ipAddress: v.string(),
    content: v.string(),
    sentiment: RoadmapFeedbackSentiment, // Assuming RoadmapFeedbackSentiment is a string
    isApproved: v.boolean(),
    convertedToFeatureId: v.optional(v.id("Feature")),
    convertedToIssueId: v.optional(v.id("Issue")),
    convertedAt: v.optional(v.number()),
    convertedBy: v.optional(v.string()),
    conversionNotes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // No authentication for feedback, as per typical public roadmap behavior
    return await ctx.db.insert("RoadmapFeedback", {
      roadmapItemId: args.roadmapItemId,
      userId: args.userId,
      ipAddress: args.ipAddress,
      content: args.content,
      sentiment: args.sentiment,
      isApproved: args.isApproved,
      convertedToFeatureId: args.convertedToFeatureId,
      convertedToIssueId: args.convertedToIssueId,
      convertedAt: args.convertedAt,
      convertedBy: args.convertedBy,
      conversionNotes: args.conversionNotes,
      createdAt: Date.now(),
    });
  },
});


export const updateRoadmapFeedback = mutation({
  args: {
    id: v.id("RoadmapFeedback"),
    content: v.optional(v.string()),
    sentiment: RoadmapFeedbackSentiment,
    isApproved: v.optional(v.boolean()),
    convertedToFeatureId: v.optional(v.id("Feature")),
    convertedToIssueId: v.optional(v.id("Issue")),
    convertedAt: v.optional(v.number()),
    convertedBy: v.optional(v.string()),
    conversionNotes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    const { id, ...updates } = args;
    return await ctx.db.patch(id, updates);
  },
});

export const deleteRoadmapFeedback = mutation({
  args: {
    id: v.id("RoadmapFeedback"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    await ctx.db.delete(args.id);
    return { success: true };
  },
});
