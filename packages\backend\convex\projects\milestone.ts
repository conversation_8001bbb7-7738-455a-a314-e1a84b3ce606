import { v } from "convex/values";
import { query } from "../_generated/server";
import { betterAuth } from "../auth";

export const getMilestones = query({
  args: {
    id: v.id("Project"),
  },
  handler: async (ctx, { id }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const milestones = await ctx.db
      .query("Milestone")
      .withIndex("by_project", (q) => q.eq("projectId", id))
      .collect();

    return milestones;
  },
});
