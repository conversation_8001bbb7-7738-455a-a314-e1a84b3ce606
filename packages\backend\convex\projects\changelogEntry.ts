import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { ChangelogEntryType, Importance } from "../../schema/enum";
import { betterAuth } from "../auth";

export const getChangelogEntriesByChangelog = query({
  args: {
    changelogId: v.id("roadmapChangelogs"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    return await ctx.db
      .query("ChangelogEntry")
      .filter((q) => q.eq(q.field("changelogId"), args.changelogId))
      .collect();
  },
});

export const createChangelogEntry = mutation({
  args: {
    changelogId: v.id("roadmapChangelogs"),
    type: ChangelogEntryType,
    title: v.string(),
    description: v.optional(v.string()),
    issueId: v.optional(v.id("Issue")),
    featureId: v.optional(v.id("Feature")),
    priority: v.optional(Importance),
    category: v.optional(v.string()),
    breaking: v.boolean(),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    return await ctx.db.insert("ChangelogEntry", {
      changelogId: args.changelogId,
      type: args.type,
      title: args.title,
      description: args.description,
      issueId: args.issueId,
      featureId: args.featureId,
      priority: args.priority,
      category: args.category,
      breaking: args.breaking,
    });
  },
});

export const updateChangelogEntry = mutation({
  args: {
    id: v.id("ChangelogEntry"),
    type: v.optional(ChangelogEntryType),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    issueId: v.optional(v.id("Issue")),
    featureId: v.optional(v.id("Feature")),
    priority: v.optional(Importance),
    category: v.optional(v.string()),
    breaking: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    const { id, ...updates } = args;
    return await ctx.db.patch(id, updates);
  },
});

export const deleteChangelogEntry = mutation({
  args: {
    id: v.id("ChangelogEntry"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    await ctx.db.delete(args.id);
    return { success: true };
  },
});
