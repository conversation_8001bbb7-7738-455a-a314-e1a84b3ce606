# Implementation Plan

- [ ] 1. Extend Convex waitlist functions with missing CRUD operations
  - Add comprehensive waitlist management functions to match server action functionality
  - Implement proper authentication and organization scoping
  - Add data transformation to match UI expectations
  - _Requirements: 2.1, 2.2, 4.1, 4.3_

- [ ] 1.1 Implement getAllWaitlists query with stats
  - Create query function that fetches all waitlists for the current organization
  - Include embedded stats (totalEntries, verifiedEntries, totalReferrals) for each waitlist
  - Transform data to match the format expected by existing UI components
  - Add proper authentication using betterAuth
  - _Requirements: 2.1, 4.3_

- [ ] 1.2 Implement getWaitlist query with integration details
  - Create query function to fetch single waitlist by ID
  - Include project information and email sync integration status
  - Add organization scoping for security
  - Transform data to match UI expectations
  - _Requirements: 2.1, 4.3_

- [ ] 1.3 Implement waitlist CRUD mutations
  - Create createWaitlist mutation with integration usage handling
  - Create updateWaitlist mutation with integration management
  - Create deleteWaitlist mutation with proper cleanup
  - Add proper error handling and validation
  - _Requirements: 2.2, 5.1, 5.2, 5.3_

- [ ] 1.4 Implement waitlist analytics query
  - Create getWaitlistAnalytics query with comprehensive stats
  - Calculate conversion rates, UTM breakdowns, and status distributions
  - Include recent activity metrics and referral statistics
  - Transform data to match existing analytics component expectations
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [ ] 1.5 Implement waitlist entry management functions
  - Create getWaitlistEntries query with filtering and pagination
  - Create updateEntryStatus mutation with timestamp handling
  - Create deleteEntry mutation for entry removal
  - Add search and status filtering capabilities
  - _Requirements: 2.1, 2.2, 5.4_

- [ ] 1.6 Implement utility functions and actions
  - Create checkSlugAvailability action for slug validation
  - Add helper functions for data transformation
  - Implement proper error handling patterns
  - Add logging for debugging and monitoring
  - _Requirements: 2.3, 4.3_

- [ ] 2. Convert main waitlist page to client-only data fetching
  - Remove server-side data fetching and prefetching
  - Update page component to only render client components
  - Remove TanStack Query dependencies
  - _Requirements: 3.1, 3.2, 3.4_

- [ ] 2.1 Update waitlist page.tsx to remove server data fetching
  - Remove getAllWaitlists import and server-side call
  - Remove queryClient.prefetchQuery usage
  - Remove getQueryClient and HydrationBoundary dependencies
  - Keep only the Header and WaitlistClient rendering
  - _Requirements: 3.1, 3.2_

- [ ] 2.2 Update waitlist-client.tsx to use Convex hooks
  - Replace useQuery from @tanstack/react-query with convex/react
  - Replace useMutation from @tanstack/react-query with convex/react
  - Remove queryClient and invalidateQueries usage
  - Update import statements to use Convex API
  - _Requirements: 2.1, 2.2, 2.4_

- [ ] 2.3 Update error handling and loading states
  - Adapt loading state handling for Convex query patterns
  - Update error handling to work with Convex mutations
  - Ensure proper handling of undefined vs null states
  - Maintain existing user experience during loading
  - _Requirements: 4.4_

- [ ] 3. Convert individual waitlist page components
  - Update waitlist detail pages to use Convex hooks
  - Remove server-side prefetching from individual waitlist pages
  - Convert all child components to use real-time data
  - _Requirements: 3.1, 3.2, 3.3_

- [ ] 3.1 Update waitlist [id]/page.tsx to remove server fetching
  - Remove getWaitlist, getWaitlistAnalytics server action imports
  - Remove getAllWaitlistEntries server action import
  - Remove queryClient prefetching logic
  - Remove HydrationBoundary wrapper
  - Keep only client component rendering
  - _Requirements: 3.1, 3.2_

- [ ] 3.2 Convert waitlist-manager.tsx to use Convex hooks
  - Replace useQuery and useMutation from TanStack with Convex hooks
  - Update getWaitlist and updateWaitlist to use Convex API
  - Remove queryKeys and query invalidation logic
  - Update error handling for Convex patterns
  - _Requirements: 2.1, 2.2, 2.4_

- [ ] 3.3 Convert waitlist-overview.tsx to use Convex hooks
  - Replace waitlistEntryActions imports with Convex API calls
  - Update useQuery and useMutation to use Convex hooks
  - Remove TanStack Query dependencies
  - Ensure real-time updates for entry status changes
  - _Requirements: 2.1, 2.2, 5.4_

- [ ] 3.4 Convert waitlist-analytics.tsx to use Convex hooks
  - Replace analytics data fetching with Convex query
  - Update component to use real-time analytics data
  - Remove any server action dependencies
  - Ensure charts update automatically with new data
  - _Requirements: 6.1, 6.2, 6.3, 6.4_

- [ ] 4. Convert waitlist form components
  - Update waitlist creation and editing forms to use Convex
  - Remove server action dependencies from form components
  - Ensure real-time validation and feedback
  - _Requirements: 2.2, 5.1, 5.2_

- [ ] 4.1 Update waitlist-form.tsx to use Convex mutations
  - Replace waitlistActions imports with Convex API
  - Update form submission to use Convex createWaitlist/updateWaitlist
  - Remove server action error handling patterns
  - Add proper Convex mutation error handling
  - _Requirements: 2.2, 5.1, 5.2_

- [ ] 4.2 Update new waitlist page to use Convex
  - Remove any server-side dependencies from new waitlist page
  - Ensure form uses Convex mutations for creation
  - Update navigation after successful creation
  - _Requirements: 2.2, 5.1_

- [ ] 4.3 Update edit waitlist page to use Convex
  - Remove server-side data fetching from edit page
  - Update form to use Convex queries and mutations
  - Ensure real-time updates during editing
  - _Requirements: 2.1, 2.2, 5.2_

- [ ] 5. Remove TanStack Query dependencies and cleanup
  - Remove all @tanstack/react-query imports from waitlist components
  - Remove query client usage and prefetching logic
  - Clean up unused server action files
  - Update import statements throughout
  - _Requirements: 2.4, 3.4_

- [ ] 5.1 Remove TanStack Query imports from all waitlist components
  - Remove useQuery, useMutation, useQueryClient imports
  - Remove queryKeys usage
  - Remove query invalidation logic
  - Update all import statements to use Convex hooks
  - _Requirements: 2.4_

- [ ] 5.2 Clean up server action files
  - Remove or deprecate apps/web/actions/waitlist/index.ts
  - Remove or deprecate apps/web/actions/waitlist/entries.ts
  - Update any remaining imports that reference these files
  - Document the migration for future reference
  - _Requirements: 2.4_

- [ ] 5.3 Update query client and provider usage
  - Remove getQueryClient usage from waitlist pages
  - Remove HydrationBoundary wrappers
  - Ensure ConvexClientProvider is properly configured
  - Remove any waitlist-specific query client configuration
  - _Requirements: 3.4_

- [ ] 6. Add performance optimizations and indexes
  - Add database indexes for frequently queried fields
  - Optimize query performance for large datasets
  - Add proper pagination and filtering
  - _Requirements: 4.2_

- [ ] 6.1 Add database indexes for waitlist queries
  - Add index for waitlist organizationId queries
  - Add compound indexes for waitlist entry filtering
  - Add indexes for analytics queries
  - Test query performance improvements
  - _Requirements: 4.2_

- [ ] 6.2 Implement proper pagination for large datasets
  - Add pagination support to getWaitlistEntries query
  - Update UI components to handle paginated data
  - Add loading states for pagination
  - Optimize memory usage for large waitlists
  - _Requirements: 4.2_

- [ ] 7. Test real-time functionality and fix issues
  - Test real-time updates across multiple browser tabs
  - Verify all CRUD operations work in real-time
  - Test error handling and edge cases
  - Ensure UI remains responsive during updates
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 4.4_

- [ ] 7.1 Test real-time waitlist updates
  - Open multiple browser tabs and test waitlist creation/deletion
  - Verify waitlist stats update in real-time across tabs
  - Test waitlist editing updates propagate immediately
  - Ensure proper error handling for concurrent modifications
  - _Requirements: 1.1, 1.2, 1.3_

- [ ] 7.2 Test real-time entry management
  - Test entry status changes update across all views
  - Verify entry deletion removes items immediately
  - Test entry filtering and search work with real-time data
  - Ensure analytics update when entries change
  - _Requirements: 1.4, 5.4, 6.1, 6.2, 6.3, 6.4_

- [ ] 7.3 Test error handling and edge cases
  - Test behavior when network connection is lost
  - Verify proper error messages for failed operations
  - Test concurrent user modifications
  - Ensure UI remains stable during errors
  - _Requirements: 4.4_