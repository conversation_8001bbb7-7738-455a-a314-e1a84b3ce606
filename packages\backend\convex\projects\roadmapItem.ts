import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { IssueStatus, IssueLabel, Importance } from "../../schema/enum";
import { betterAuth } from "../auth";

export const getRoadmapItemsByRoadmap = query({
  args: {
    roadmapId: v.id("PublicRoadmap"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    return await ctx.db
      .query("RoadmapItem")
      .filter((q) => q.eq(q.field("roadmapId"), args.roadmapId))
      .collect();
  },
});

// Public version for landing page - no auth required
export const getPublicRoadmapItemsByRoadmap = query({
  args: {
    roadmapId: v.id("PublicRoadmap"),
  },
  handler: async (ctx, args) => {
    // Verify the roadmap exists and is public
    const roadmap = await ctx.db.get(args.roadmapId);
    if (!roadmap || !roadmap.isPublic) {
      return [];
    }
    
    return await ctx.db
      .query("RoadmapItem")
      .filter((q) => q.eq(q.field("roadmapId"), args.roadmapId))
      .filter((q) => q.eq(q.field("isPublic"), true))
      .collect();
  },
});

export const createRoadmapItem = mutation({
  args: {
    roadmapId: v.id("PublicRoadmap"),
    title: v.string(),
    description: v.string(),
    status: IssueStatus,
    category: IssueLabel,
    isPublic: v.boolean(),
    priority: Importance,
    targetDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    return await ctx.db.insert("RoadmapItem", {
      roadmapId: args.roadmapId,
      title: args.title,
      description: args.description,
      status: args.status,
      category: args.category,
      isPublic: args.isPublic,
      priority: args.priority,
      targetDate: args.targetDate,
    });
  },
});

export const updateRoadmapItem = mutation({
  args: {
    id: v.id("RoadmapItem"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    status: v.optional(IssueStatus),
    category: v.optional(IssueLabel),
    isPublic: v.optional(v.boolean()),
    priority: v.optional(Importance),
    targetDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    const { id, ...updates } = args;
    return await ctx.db.patch(id, updates);
  },
});

export const deleteRoadmapItem = mutation({
  args: {
    id: v.id("RoadmapItem"),
  },
  handler: async (ctx, args) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    await ctx.db.delete(args.id);
    return { success: true };
  },
});
