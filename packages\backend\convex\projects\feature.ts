import { v } from "convex/values";
import { query, mutation, action, internalMutation } from "../_generated/server";
import { betterAuth } from "../auth";
import { FeaturePhase, Importance } from "../../schema/enum";
import { internal } from "../_generated/api";

export const getFeatures = query({
  args: {
    id: v.id("Project"),
  },
  handler: async (ctx, { id }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const features = await ctx.db
      .query("Feature")
      .withIndex("by_project", (q) => q.eq("projectId", id))
      .collect();

    // Enrich features with related data
    const enrichedFeatures = await Promise.all(
      features.map(async (feature) => {
        const assignedTo = feature.assignedToId ? await ctx.db.get(feature.assignedToId) : null;
        const parentFeature = feature.parentFeatureId ? await ctx.db.get(feature.parentFeatureId) : null;

        return {
          ...feature,
          assignedTo,
          parentFeature,
          isSubFeature: !!feature.parentFeatureId,
        };
      })
    );

    return enrichedFeatures;
  },
});

export const getFeatureById = query({
  args: {
    id: v.id("Feature"),
  },
  handler: async (ctx, { id }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const feature = await ctx.db.get(id);

    if (!feature || feature.organizationId !== session.organizationId) {
      return null;
    }

    // Get related data
    const assignedTo = feature.assignedToId ? await ctx.db.get(feature.assignedToId) : null;
    const parentFeature = feature.parentFeatureId ? await ctx.db.get(feature.parentFeatureId) : null;
    const project = await ctx.db.get(feature.projectId);

    // Get sub-features
    const subFeatures = await ctx.db
      .query("Feature")
      .filter((q) => q.eq(q.field("parentFeatureId"), id))
      .collect();

    // Get dependencies (features this feature depends on)
    const dependencies = await ctx.db
      .query("FeatureDependency")
      .filter((q) => q.eq(q.field("featureId"), id))
      .collect();

    const dependencyDetails = await Promise.all(
      dependencies.map(async (dep) => {
        const dependency = await ctx.db.get(dep.dependencyId);
        return { ...dep, dependency };
      })
    );

    // Get dependents (features that depend on this feature)
    const dependents = await ctx.db
      .query("FeatureDependency")
      .filter((q) => q.eq(q.field("dependencyId"), id))
      .collect();

    const dependentDetails = await Promise.all(
      dependents.map(async (dep) => {
        const dependentFeature = await ctx.db.get(dep.featureId);
        return { ...dep, feature: dependentFeature };
      })
    );

    // Get feature links
    const featureLinks = await ctx.db
      .query("FeatureLink")
      .filter((q) => q.eq(q.field("featureId"), id))
      .collect();

    return {
      ...feature,
      assignedTo,
      parentFeature,
      subFeatures,
      project,
      dependencies: dependencyDetails,
      dependentOn: dependentDetails,
      FeatureLink: featureLinks,
    };
  },
});

export const updateFeature = mutation({
  args: {
    id: v.id("Feature"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    phase: v.optional(FeaturePhase),
    businessValue: v.optional(v.number()),
    estimatedEffort: v.optional(v.number()),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    priority: v.optional(Importance),
    assignedToId: v.optional(v.id("User")),
    parentFeatureId: v.optional(v.id("Feature")),
    milestoneId: v.optional(v.id("Milestone")),
  },
  handler: async (ctx, { id, ...updates }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const feature = await ctx.db.get(id);

    if (!feature || feature.organizationId !== session.organizationId) {
      throw new Error("Feature not found or access denied");
    }

    // Filter out undefined values to only update provided fields
    const filteredUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    await ctx.db.patch(id, filteredUpdates);

    // Return the updated feature with relations
    return await ctx.db.get(id);
  },
});

export const validateFeatureCompletion = query({
  args: {
    id: v.id("Feature"),
  },
  handler: async (ctx, { id }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const feature = await ctx.db.get(id);

    if (!feature || feature.organizationId !== session.organizationId) {
      return {
        canComplete: false,
        blockers: [],
        totalDependencies: 0,
        completedDependencies: 0,
        error: "Feature not found",
      };
    }

    // Get dependencies
    const dependencies = await ctx.db
      .query("FeatureDependency")
      .filter((q) => q.eq(q.field("featureId"), id))
      .collect();

    const dependencyDetails = await Promise.all(
      dependencies.map(async (dep) => {
        const dependency = await ctx.db.get(dep.dependencyId);
        return dependency;
      })
    );

    // Check if all dependencies are completed (LIVE phase)
    const blockers = dependencyDetails.filter(
      (dep) => dep && dep.phase !== "LIVE"
    );

    const canComplete = blockers.length === 0;
    const blockerDetails = blockers.map((dep) => ({
      id: dep?._id,
      name: dep?.name,
      phase: dep?.phase,
    }));

    return {
      canComplete,
      blockers: blockerDetails,
      totalDependencies: dependencies.length,
      completedDependencies: dependencies.length - blockers.length,
    };
  },
});

export const getFeatureLinks = query({
  args: {
    featureId: v.id("Feature"),
  },
  handler: async (ctx, { featureId }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const feature = await ctx.db.get(featureId);

    if (!feature || feature.organizationId !== session.organizationId) {
      throw new Error("Feature not found or access denied");
    }

    const links = await ctx.db
      .query("FeatureLink")
      .filter((q) => q.eq(q.field("featureId"), featureId))
      .collect();

    return links;
  },
});

export const addFeatureLink = mutation({
  args: {
    featureId: v.id("Feature"),
    url: v.string(),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    image: v.optional(v.string()),
    siteName: v.optional(v.string()),
    favicon: v.optional(v.string()),
  },
  handler: async (ctx, { featureId, url, title, description, image, siteName, favicon }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const feature = await ctx.db.get(featureId);

    if (!feature || feature.organizationId !== session.organizationId) {
      throw new Error("Feature not found or access denied");
    }

    // Validate URL format
    let parsedUrl;
    try {
      parsedUrl = new URL(url);
    } catch {
      throw new Error("Invalid URL format");
    }

    // Generate default metadata if not provided
    const domain = parsedUrl.hostname.replace("www.", "");
    const defaultTitle = title || domain;
    const defaultFavicon = favicon || `https://www.google.com/s2/favicons?domain=${url}&sz=64`;

    const linkId = await ctx.db.insert("FeatureLink", {
      featureId,
      url,
      title: defaultTitle,
      description,
      image,
      siteName: siteName || domain,
      favicon: defaultFavicon,
      organizationId: session.organizationId,
    });

    return await ctx.db.get(linkId);
  },
});

export const deleteFeatureLink = mutation({
  args: {
    id: v.id("FeatureLink"),
  },
  handler: async (ctx, { id }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    const link = await ctx.db.get(id);

    if (!link || link.organizationId !== session.organizationId) {
      throw new Error("Link not found or access denied");
    }

    await ctx.db.delete(id);

    return { success: true };
  },
});

export const createFeature = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    projectId: v.id("Project"),
    phase: v.optional(FeaturePhase),
    businessValue: v.optional(v.number()),
    estimatedEffort: v.optional(v.number()),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    priority: v.optional(Importance),
    assignedToId: v.optional(v.id("User")),
    parentFeatureId: v.optional(v.id("Feature")),
    milestoneId: v.optional(v.id("Milestone")),
  },
  handler: async (ctx, args) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    // Verify project exists and user has access
    const project = await ctx.db.get(args.projectId);
    if (!project || project.organizationId !== session.organizationId) {
      throw new Error("Project not found or access denied");
    }

    // If parentFeatureId is provided, verify it exists and belongs to the same project
    if (args.parentFeatureId) {
      const parentFeature = await ctx.db.get(args.parentFeatureId);
      if (!parentFeature || parentFeature.organizationId !== session.organizationId) {
        throw new Error("Parent feature not found or access denied");
      }
      if (parentFeature.projectId !== args.projectId) {
        throw new Error("Parent feature must belong to the same project");
      }
    }

    // Create the feature
    const featureId = await ctx.db.insert("Feature", {
      name: args.name,
      description: args.description,
      projectId: args.projectId,
      phase: args.phase || "DISCOVERY",
      businessValue: args.businessValue,
      estimatedEffort: args.estimatedEffort,
      startDate: args.startDate,
      endDate: args.endDate,
      priority: args.priority || "MEDIUM",
      assignedToId: args.assignedToId,
      parentFeatureId: args.parentFeatureId,
      milestoneId: args.milestoneId,
      organizationId: session.organizationId,
    });

    // Return the created feature
    const createdFeature = await ctx.db.get(featureId);
    return { id: featureId, ...createdFeature };
  },
});

// Helper query to get project for feature generation
export const getProjectForGeneration = query({
  args: {
    projectId: v.id("Project"),
    organizationId: v.string(),
  },
  handler: async (ctx, { projectId, organizationId }) => {
    const project = await ctx.db.get(projectId);

    if (!project || project.organizationId !== organizationId) {
      return null;
    }

    return project;
  },
});

export const generateFeatures = action({
  args: {
    projectId: v.id("Project"),
  },
  handler: async (ctx, { projectId }) => {
    const session = await betterAuth.getAuthUser(ctx);

    if (!session) {
      throw new Error("Not authenticated");
    }

    // Get project directly from database
    const project = await ctx.runQuery(internal.projects.feature.getProjectForGeneration, {
      projectId,
      organizationId: session.organizationId,
    });

    if (!project) {
      throw new Error("Project not found or access denied");
    }

    try {
      // Generate features using AI based on project context
      const generatedFeatures = await generateFeaturesWithAI(project);

      // Create features in the database using the existing createFeature mutation
      const createdFeatures = [];
      for (const featureData of generatedFeatures) {
        const result = await ctx.runMutation(internal.projects.feature.createFeature, {
          name: featureData.name,
          description: featureData.description,
          projectId: projectId,
          phase: "DISCOVERY",
          priority: featureData.priority || "MEDIUM",
          businessValue: featureData.businessValue,
          estimatedEffort: featureData.estimatedEffort,
        });

        createdFeatures.push(result.id);
      }

      return {
        success: true,
        message: `Successfully generated ${createdFeatures.length} features`,
        featuresCreated: createdFeatures.length,
      };
    } catch (error) {
      console.error("Error generating features:", error);
      throw new Error("Failed to generate features");
    }
  },
});

// AI-powered feature generation function
async function generateFeaturesWithAI(project: any) {
  // This is a simplified implementation
  // In a real implementation, you would use an AI service like OpenAI, Anthropic, etc.

  const baseFeatures = [
    {
      name: "User Authentication",
      description: "Implement secure user registration, login, and password management",
      priority: "HIGH" as const,
      businessValue: 8,
      estimatedEffort: 40,
    },
    {
      name: "Dashboard Overview",
      description: "Create a comprehensive dashboard showing key metrics and recent activity",
      priority: "HIGH" as const,
      businessValue: 7,
      estimatedEffort: 32,
    },
    {
      name: "User Profile Management",
      description: "Allow users to manage their profile information and preferences",
      priority: "MEDIUM" as const,
      businessValue: 5,
      estimatedEffort: 24,
    },
    {
      name: "Data Export/Import",
      description: "Enable users to export their data and import from external sources",
      priority: "MEDIUM" as const,
      businessValue: 6,
      estimatedEffort: 48,
    },
    {
      name: "Notification System",
      description: "Implement email and in-app notifications for important events",
      priority: "MEDIUM" as const,
      businessValue: 6,
      estimatedEffort: 36,
    },
  ];

  // Customize features based on project context
  const customizedFeatures = baseFeatures.map(feature => ({
    ...feature,
    name: `${project.name} - ${feature.name}`,
    description: `${feature.description} for the ${project.name} project`,
  }));

  return customizedFeatures;
}
