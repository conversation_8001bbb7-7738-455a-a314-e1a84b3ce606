import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { betterAuth } from "../auth";

export const getRoadmapsByProject = query({
  args: { id: v.id("Project") },
  handler: async (ctx, { id }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) return null;

    const roadmaps = await ctx.db
      .query("PublicRoadmap")
      .withIndex("byProject", (q) => q.eq("projectId", id))
      .collect();

    return roadmaps;
  },
});

export const getRoadmapBySlug = query({
  args: { slug: v.string() },
  handler: async (ctx, { slug }) => {
    const roadmap = await ctx.db
      .query("PublicRoadmap")
      .withIndex("bySlug", (q) => q.eq("slug", slug))
      .unique();

    return roadmap;
  },
});

export const createRoadmap = mutation({
  args: {
    projectId: v.id("Project"),
    name: v.string(),
    slug: v.string(),
    description: v.string(),
    allowFeedback: v.boolean(),
    allowVoting: v.boolean(),
    isPublic: v.boolean(),
  },
  handler: async (
    ctx,
    { projectId, name, slug, description, allowFeedback, allowVoting, isPublic }
  ) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Unauthorized");

    const roadmapId = await ctx.db.insert("PublicRoadmap", {
      projectId,
      name,
      slug,
      description,
      allowFeedback,
      allowVoting,
      isPublic,
    });

    return roadmapId;
  },
});

export const updateRoadmap = mutation({
  args: {
    id: v.id("PublicRoadmap"),
    name: v.optional(v.string()),
    slug: v.optional(v.string()),
    description: v.optional(v.string()),
  },
  handler: async (ctx, { id, name, slug, description }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Unauthorized");

    const existingRoadmap = await ctx.db.get(id);
    if (!existingRoadmap) throw new Error("Roadmap not found");

    await ctx.db.patch(id, {
      ...(name && { name }),
      ...(slug && { slug }),
      ...(description && { description }),
    });

    return true;
  },
});

export const getRoadmapById = query({
  args: { id: v.id("PublicRoadmap") },
  handler: async (ctx, { id }) => {
    const roadmap = await ctx.db.get(id);
    return roadmap;
  },
});

// Get all roadmaps for the user's organization with enriched stats
export const getAllRoadmapsByOrg = query({
  args: {},
  handler: async (ctx) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Unauthorized");

    // Get all projects for the organization
    const projects = await ctx.db
      .query("Project")
      .filter((q) => q.eq(q.field("organizationId"), auth.organizationId))
      .collect();

    const projectIds = projects.map((p) => p._id);

    // Get all roadmaps for these projects
    const roadmaps = await ctx.db
      .query("PublicRoadmap")
      .filter((q) =>
        q.or(
          ...projectIds.map(projectId =>
            q.eq(q.field("projectId"), projectId)
          )
        )
      )
      .collect();

    // Enrich roadmaps with stats
    const enrichedRoadmaps = await Promise.all(
      roadmaps.map(async (roadmap) => {
        // Get project info
        const project = projects.find(p => p._id === roadmap.projectId);

        // Get roadmap items
        const items = await ctx.db
          .query("RoadmapItem")
          .filter((q) => q.eq(q.field("roadmapId"), roadmap._id))
          .collect();

        // Get changelogs count
        const changelogsCount = await ctx.db
          .query("RoadmapChangelog")
          .filter((q) => q.eq(q.field("roadmapId"), roadmap._id))
          .collect();

        // Get feature requests count
        const featureRequestsCount = await ctx.db
          .query("FeatureRequest")
          .filter((q) => q.eq(q.field("roadmapId"), roadmap._id))
          .collect();

        // Calculate vote and feedback counts for all items
        let totalVotes = 0;
        let totalFeedback = 0;
        const enrichedItems = await Promise.all(
          items.map(async (item) => {
            const votes = await ctx.db
              .query("RoadmapVote")
              .filter((q) => q.eq(q.field("roadmapItemId"), item._id))
              .collect();

            const feedback = await ctx.db
              .query("RoadmapFeedback")
              .filter((q) => q.eq(q.field("roadmapItemId"), item._id))
              .collect();

            totalVotes += votes.length;
            totalFeedback += feedback.length;

            return {
              ...item,
              voteCount: votes.length,
              feedbackCount: feedback.length,
            };
          })
        );

        return {
          ...roadmap,
          project: project ? {
            id: project._id,
            name: project.name,
            status: project.status,
            description: project.description,
            platform: project.platform,
            createdAt: project._creationTime,
          } : null,
          items: enrichedItems,
          stats: {
            totalItems: items.length,
            totalChangelogs: changelogsCount.length,
            totalFeatureRequests: featureRequestsCount.length,
            totalVotes,
            totalFeedback,
            lastUpdated: roadmap._creationTime,
          },
        };
      })
    );

    return enrichedRoadmaps;
  },
});

// Delete a roadmap
export const deleteRoadmap = mutation({
  args: { id: v.id("PublicRoadmap") },
  handler: async (ctx, { id }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Unauthorized");

    const roadmap = await ctx.db.get(id);
    if (!roadmap) throw new Error("Roadmap not found");

    // Verify the roadmap belongs to the user's organization
    const project = await ctx.db.get(roadmap.projectId);
    if (!project || project.organizationId !== auth.organizationId) {
      throw new Error("Unauthorized to delete this roadmap");
    }

    // Delete all related data
    // Delete roadmap items
    const items = await ctx.db
      .query("RoadmapItem")
      .filter((q) => q.eq(q.field("roadmapId"), id))
      .collect();

    for (const item of items) {
      // Delete votes for this item
      const votes = await ctx.db
        .query("RoadmapVote")
        .filter((q) => q.eq(q.field("roadmapItemId"), item._id))
        .collect();
      for (const vote of votes) {
        await ctx.db.delete(vote._id);
      }

      // Delete feedback for this item
      const feedback = await ctx.db
        .query("RoadmapFeedback")
        .filter((q) => q.eq(q.field("roadmapItemId"), item._id))
        .collect();
      for (const fb of feedback) {
        await ctx.db.delete(fb._id);
      }

      // Delete the item
      await ctx.db.delete(item._id);
    }

    // Delete changelogs
    const changelogs = await ctx.db
      .query("RoadmapChangelog")
      .filter((q) => q.eq(q.field("roadmapId"), id))
      .collect();
    for (const changelog of changelogs) {
      await ctx.db.delete(changelog._id);
    }

    // Delete feature requests
    const featureRequests = await ctx.db
      .query("FeatureRequest")
      .filter((q) => q.eq(q.field("roadmapId"), id))
      .collect();
    for (const request of featureRequests) {
      await ctx.db.delete(request._id);
    }

    // Finally delete the roadmap
    await ctx.db.delete(id);

    return true;
  },
});

export const deleteRoadmap = mutation({
  args: { id: v.id("PublicRoadmap") },
  handler: async (ctx, { id }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Unauthorized");

    const existingRoadmap = await ctx.db.get(id);
    if (!existingRoadmap) throw new Error("Roadmap not found");

    await ctx.db.delete(id);

    return true;
  },
});
