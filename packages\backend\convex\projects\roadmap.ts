import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { betterAuth } from "../auth";

export const getRoadmapsByProject = query({
  args: { id: v.id("Project") },
  handler: async (ctx, { id }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) return null;

    const roadmaps = await ctx.db
      .query("PublicRoadmap")
      .withIndex("byProject", (q) => q.eq("projectId", id))
      .collect();

    return roadmaps;
  },
});

export const getRoadmapBySlug = query({
  args: { slug: v.string() },
  handler: async (ctx, { slug }) => {
    const roadmap = await ctx.db
      .query("PublicRoadmap")
      .withIndex("bySlug", (q) => q.eq("slug", slug))
      .unique();

    return roadmap;
  },
});

export const createRoadmap = mutation({
  args: {
    projectId: v.id("Project"),
    name: v.string(),
    slug: v.string(),
    description: v.string(),
    allowFeedback: v.boolean(),
    allowVoting: v.boolean(),
    isPublic: v.boolean(),
  },
  handler: async (
    ctx,
    { projectId, name, slug, description, allowFeedback, allowVoting, isPublic }
  ) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Unauthorized");

    const roadmapId = await ctx.db.insert("PublicRoadmap", {
      projectId,
      name,
      slug,
      description,
      allowFeedback,
      allowVoting,
      isPublic,
    });

    return roadmapId;
  },
});

export const updateRoadmap = mutation({
  args: {
    id: v.id("PublicRoadmap"),
    name: v.optional(v.string()),
    slug: v.optional(v.string()),
    description: v.optional(v.string()),
  },
  handler: async (ctx, { id, name, slug, description }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Unauthorized");

    const existingRoadmap = await ctx.db.get(id);
    if (!existingRoadmap) throw new Error("Roadmap not found");

    await ctx.db.patch(id, {
      ...(name && { name }),
      ...(slug && { slug }),
      ...(description && { description }),
    });

    return true;
  },
});

export const getRoadmapById = query({
  args: { id: v.id("PublicRoadmap") },
  handler: async (ctx, { id }) => {
    const roadmap = await ctx.db.get(id);
    return roadmap;
  },
});

export const deleteRoadmap = mutation({
  args: { id: v.id("PublicRoadmap") },
  handler: async (ctx, { id }) => {
    const auth = await betterAuth.getAuthUser(ctx);
    if (!auth) throw new Error("Unauthorized");

    const existingRoadmap = await ctx.db.get(id);
    if (!existingRoadmap) throw new Error("Roadmap not found");

    await ctx.db.delete(id);

    return true;
  },
});
