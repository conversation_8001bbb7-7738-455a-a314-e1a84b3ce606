import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import {
  Project,
  ActivityFeed,
  Asset,
  AssetDownload,
  AssetView,
  Issue,
  IssueDependency,
  IssueLink,
  Milestone,
  MilestoneDependency,
  PublicRoadmap,
  Referral,
  Waitlist,
  WaitlistEntry,
  Feature,
  FeatureDependency,
  FeatureLink,
  ChangelogEntry,
  FeatureRequest,
  RoadmapChangelog,
  RoadmapFeedback,
  RoadmapItem,
  RoadmapVote,
} from "../schema/project";
import {
  CompetitiveMove,
  Competitor,
  CompetitorSwot,
  Idea,
} from "../schema/idea";

import {
  ApiCall,
  ApiKey,
  Integration,
  IntegrationUsage,
} from "../schema/settings";

import {
  ideaValidation,
  validationMetrics,
  marketValidation,
  marketInsight,
  marketRegionScore,
  businessValidation,
  businessInsight,
  riskAnalysis,
  riskItem,
  productMarketFitAnalysis,
  pmfMetric,
  pmfFeedback,
  monthlyProjection,
  acquisitionChannel,
  pricingTier,
  competitorPricing,
  customerJourneyMapping,
  journeyStage,
  touchpoint,
  journeyPainPoint,
  targetAudienceSegmentation,
  audienceSegment,
  marketTrendAnalysis,
  marketTrend,
  customerNeedAnalysis,
  customerNeed,
  painPoint,
  pricingStrategyAnalysis,
} from "../schema/validation";

export default defineSchema({
  //AUTH
  User: defineTable({
    name: v.string(),
    email: v.string(),
    emailVerified: v.boolean(),
    image: v.optional(v.string()),
    role: v.optional(v.string()),
    twoFactorEnabled: v.optional(v.boolean()),
  }).index("by_email", ["email"]),

  Subscription: defineTable({
    status: v.optional(v.string()),
    organisation_id: v.id("Organization"),
    subscription_id: v.optional(v.string()),
    product_id: v.optional(v.string()),
    userId: v.optional(v.id("User")),
  })
    .index("by_organisation_id", ["organisation_id"])
    .index("by_userId", ["userId"]),

  // PROJECTS
  Project,
  ActivityFeed,
  Asset,
  AssetDownload,
  AssetView,
  Feature,
  FeatureDependency,
  FeatureLink,
  Issue,
  IssueDependency,
  IssueLink,
  Milestone,
  MilestoneDependency,
  PublicRoadmap,
  Referral,
  ChangelogEntry,
  FeatureRequest,
  RoadmapChangelog,
  RoadmapFeedback,
  RoadmapItem,
  RoadmapVote,
  Waitlist,
  WaitlistEntry,

  //   IDEAS
  CompetitiveMove,
  Competitor,
  CompetitorSwot,
  Idea,

  //SETTINGS
  ApiCall,
  ApiKey,
  Integration,
  IntegrationUsage,

  //VALIDATION
  ideaValidation,
  validationMetrics,
  marketValidation,
  marketInsight,
  marketRegionScore,
  businessValidation,
  businessInsight,
  riskAnalysis,
  riskItem,
  productMarketFitAnalysis,
  pmfMetric,
  pmfFeedback,
  monthlyProjection,
  acquisitionChannel,
  pricingTier,
  competitorPricing,
  customerJourneyMapping,
  journeyStage,
  touchpoint,
  journeyPainPoint,
  targetAudienceSegmentation,
  audienceSegment,
  marketTrendAnalysis,
  marketTrend,
  customerNeedAnalysis,
  customerNeed,
  painPoint,
  pricingStrategyAnalysis,
});
