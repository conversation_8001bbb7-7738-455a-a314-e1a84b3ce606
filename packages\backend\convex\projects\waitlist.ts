import { v } from "convex/values";
import { mutation, query } from "../_generated/server";
import { counter } from "../SharedCounter";
import { ConvexError } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

// Utility function for generating referral codes
function generateReferralCode(prefix = "", length = 10) {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  const randomChars = Array.from(
    { length },
    () => chars[Math.floor(Math.random() * chars.length)]
  ).join("");
  return `${prefix}${randomChars}`;
}

// Get waitlist by slug (public query - no auth required)
export const getWaitlistBySlug = query({
  args: {
    slug: v.string(),
  },
  handler: async (ctx, { slug }) => {
    // Find waitlist by slug
    const waitlist = await ctx.db
      .query("Waitlist")
      .withIndex("by_slug", (q) => q.eq("slug", slug))
      .first();

    if (!waitlist) {
      return null;
    }

    const [totalEntries, referralCount, project] = await Promise.all([
      counter.count(ctx, `waitlist-entries-${waitlist._id}`),
      counter.count(ctx, `waitlist-referrals-${waitlist._id}`),
      ctx.db.get(waitlist.projectId),
    ]);

    return {
      ...waitlist,
      project,
      stats: {
        totalEntries,
        joinedCount: totalEntries,
        referralCount,
      },
    };
  },
});

// Check waitlist entry status by email (mutation)
export const checkWaitlistEntry = mutation({
  args: {
    waitlistId: v.id("Waitlist"),
    email: v.string(),
  },
  handler: async (ctx, { waitlistId, email }) => {
    // Find the entry
    const entry = await ctx.db
      .query("WaitlistEntry")
      .withIndex("by_waitlist_email", (q) =>
        q.eq("waitlistId", waitlistId).eq("email", email)
      )
      .first();

    if (!entry) {
      return null;
    }

    // Get referral count from counter and referral details
    const [referralCount] = await Promise.all([
      counter.count(ctx, `waitlist-referrals-${waitlistId}`),
    ]);

    return {
      ...entry,
      referralCount,
    };
  },
});

// Join waitlist mutation
export const joinWaitlist = mutation({
  args: {
    waitlistId: v.id("Waitlist"),
    email: v.string(),
    name: v.string(),
    referralCode: v.optional(v.string()),
    utmSource: v.optional(v.string()),
    utmMedium: v.optional(v.string()),
    utmCampaign: v.optional(v.string()),
    ipAddress: v.string(),
  },
  handler: async (ctx, args) => {
    const {
      waitlistId,
      email,
      name,
      referralCode,
      utmSource,
      utmMedium,
      utmCampaign,
      ipAddress,
    } = args;

    const waitlist = await ctx.db.get(waitlistId);

    if (!waitlist) {
      throw new Error("Waitlist does not exist");
    }
    // Check if user already exists
    const existingEntry = await ctx.db
      .query("WaitlistEntry")
      .withIndex("by_waitlist_email", (q) =>
        q.eq("waitlistId", waitlistId).eq("email", email)
      )
      .first();

    if (existingEntry) {
      throw new Error("You're already on the waitlist!");
    }

    // Generate unique referral code with retries
    let attempts = 0;
    let newReferralCode: string;
    let existingCode;

    do {
      if (attempts >= 5) {
        // After 5 attempts, include a timestamp to ensure uniqueness
        newReferralCode = generateReferralCode(
          Date.now().toString(36).slice(-2)
        );
      } else {
        newReferralCode = generateReferralCode();
      }

      existingCode = await ctx.db
        .query("WaitlistEntry")
        .withIndex("by_referral_code", (q) =>
          q.eq("waitlistId", waitlist._id).eq("referralCode", newReferralCode)
        )
        .first();

      attempts++;
    } while (existingCode && attempts < 10);

    if (existingCode) {
      throw new Error("Failed to generate unique referral code");
    }

    // Get current position by incrementing total entries count
    const position =
      (await counter.count(ctx, `waitlist-entries-${waitlistId}`)) + 1;

    // Create waitlist entry
    const entryId = await ctx.db.insert("WaitlistEntry", {
      waitlistId,
      email,
      name,
      referralCode: newReferralCode,
      status: "PENDING",
      position,
      ipAddress: ipAddress || "0.0.0.0",
      utmSource,
      utmMedium,
      utmCampaign,
    });

    await counter.inc(ctx, `waitlist-entries-${waitlistId}`);

    // Handle referral if provided
    let referrerId = null;
    if (referralCode) {
      const referrer = await ctx.db
        .query("WaitlistEntry")
        .withIndex("by_referral_code", (q) =>
          q.eq("waitlistId", waitlist._id).eq("referralCode", referralCode)
        )
        .first();

      if (referrer && referrer.waitlistId === waitlistId) {
        referrerId = referrer._id;

        // Create referral record and increment referral counter
        await Promise.all([
          ctx.db.insert("Referral", {
            waitlistId,
            ipAddress,
            organizationId: waitlist.organizationId,
            referredEmail: email,
            referrerCode: referralCode,
            referrerId: referrer._id,
          }),
          counter.inc(ctx, `waitlist-referrals-${waitlistId}`),
        ]);
      }
    }

    return {
      success: true,
      entry: {
        _id: entryId,
        email,
        name,
        referralCode: newReferralCode,
        position,
        referrerId,
      },
    };
  },
});

// Get live waitlist stats (for real-time updates)
export const getLiveWaitlistStats = query({
  args: {
    waitlistId: v.id("Waitlist"),
  },
  handler: async (ctx, { waitlistId }) => {
    // Get total entries and joined count using counters
    const [totalEntries, referralCount] = await Promise.all([
      counter.count(ctx, `waitlist-entries-${waitlistId}`),
      counter.count(ctx, `waitlist-referrals-${waitlistId}`),
    ]);

    // Get today's entries count
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);

    return {
      totalEntries,
      joinedCount: totalEntries,
      referralCount,
    };
  },
});

export const getPublicWaitlistById = query({
  args: { id: v.id("Waitlist") },
  handler: async (ctx, { id }) => {
    const waitlist = await ctx.db.get(id);
    return waitlist;
  },
});

// Create a new waitlist (authenticated)
export const createWaitlist = mutation({
  args: {
    projectId: v.id("Project"),
    name: v.string(),
    slug: v.string(),
    description: v.string(),
    isPublic: v.boolean(),
    allowNameCapture: v.boolean(),
    showPosition: v.boolean(),
    showSocialProof: v.boolean(),
    customMessage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new ConvexError("Authentication required");
    }

    // Get user's organization
    const user = await ctx.db.get(userId);
    if (!user?.organizationId) {
      throw new ConvexError("User must belong to an organization");
    }

    // Check if slug is unique
    const existingWaitlist = await ctx.db
      .query("Waitlist")
      .withIndex("by_slug", (q) => q.eq("slug", args.slug))
      .first();

    if (existingWaitlist) {
      throw new ConvexError("Slug already exists");
    }

    // Create waitlist
    const waitlistId = await ctx.db.insert("Waitlist", {
      ...args,
      organizationId: user.organizationId,
      createdById: userId,
    });

    return waitlistId;
  },
});

// Get waitlist by ID (authenticated)
export const getWaitlist = query({
  args: { id: v.id("Waitlist") },
  handler: async (ctx, { id }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new ConvexError("Authentication required");
    }

    const user = await ctx.db.get(userId);
    if (!user?.organizationId) {
      throw new ConvexError("User must belong to an organization");
    }

    const waitlist = await ctx.db.get(id);
    if (!waitlist || waitlist.organizationId !== user.organizationId) {
      throw new ConvexError("Waitlist not found or access denied");
    }

    const project = await ctx.db.get(waitlist.projectId);

    return {
      ...waitlist,
      project,
    };
  },
});

// Get all waitlists for organization (authenticated)
export const getAllWaitlists = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new ConvexError("Authentication required");
    }

    const user = await ctx.db.get(userId);
    if (!user?.organizationId) {
      throw new ConvexError("User must belong to an organization");
    }

    const waitlists = await ctx.db
      .query("Waitlist")
      .filter((q) => q.eq(q.field("organizationId"), user.organizationId))
      .collect();

    // Get stats for each waitlist
    const waitlistsWithStats = await Promise.all(
      waitlists.map(async (waitlist) => {
        const [totalEntries, referralCount, project] = await Promise.all([
          counter.count(ctx, `waitlist-entries-${waitlist._id}`),
          counter.count(ctx, `waitlist-referrals-${waitlist._id}`),
          ctx.db.get(waitlist.projectId),
        ]);

        // Get verified entries count
        const verifiedEntries = await ctx.db
          .query("WaitlistEntry")
          .withIndex("by_waitlist", (q) => q.eq("waitlistId", waitlist._id))
          .filter((q) => q.neq(q.field("verifiedAt"), undefined))
          .collect();

        return {
          ...waitlist,
          project,
          stats: {
            totalEntries,
            verifiedEntries: verifiedEntries.length,
            totalReferrals: referralCount,
          },
        };
      })
    );

    return waitlistsWithStats;
  },
});

// Update waitlist (authenticated)
export const updateWaitlist = mutation({
  args: {
    id: v.id("Waitlist"),
    name: v.optional(v.string()),
    slug: v.optional(v.string()),
    description: v.optional(v.string()),
    isPublic: v.optional(v.boolean()),
    allowNameCapture: v.optional(v.boolean()),
    showPosition: v.optional(v.boolean()),
    showSocialProof: v.optional(v.boolean()),
    customMessage: v.optional(v.string()),
  },
  handler: async (ctx, { id, ...updates }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new ConvexError("Authentication required");
    }

    const user = await ctx.db.get(userId);
    if (!user?.organizationId) {
      throw new ConvexError("User must belong to an organization");
    }

    const waitlist = await ctx.db.get(id);
    if (!waitlist || waitlist.organizationId !== user.organizationId) {
      throw new ConvexError("Waitlist not found or access denied");
    }

    // Check slug uniqueness if updating slug
    if (updates.slug && updates.slug !== waitlist.slug) {
      const existingWaitlist = await ctx.db
        .query("Waitlist")
        .withIndex("by_slug", (q) => q.eq("slug", updates.slug))
        .first();

      if (existingWaitlist) {
        throw new ConvexError("Slug already exists");
      }
    }

    await ctx.db.patch(id, updates);
    return await ctx.db.get(id);
  },
});
