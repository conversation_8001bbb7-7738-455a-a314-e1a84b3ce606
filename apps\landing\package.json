{"name": "landing", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack -p 3002", "build": "next build", "start": "next start -p 3002", "typecheck": "tsc --noEmit", "lint": "biome check .", "format": "biome format . --write", "test": "vitest run"}, "dependencies": {"@ai-sdk/google": "^1.2.22", "@ai-sdk/openai": "^1.3.23", "@convex-dev/better-auth": "^0.7.13", "convex": "^1.25.4", "@hookform/resolvers": "^5.2.1", "@next/swc-wasm-nodejs": "15.4.4", "@prisma/nextjs-monorepo-workaround-plugin": "^6.13.0", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.84.0", "@tanstack/react-table": "^8.21.3", "@types/node": "24.1.0", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@workspace/backend": "workspace:*", "@workspace/ui": "workspace:*", "ai": "^4.3.19", "better-auth": "^1.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dotted-map": "^2.2.3", "eslint": "9.32.0", "eslint-config-next": "15.4.4", "framer-motion": "^12.23.12", "geist": "^1.4.2", "loops": "^5.0.1", "lucide-react": "^0.528.0", "moment": "^2.30.1", "next": "15.4.4", "next-themes": "^0.4.6", "openai": "^5.11.0", "postcss": "8.5.6", "react": "19.1.0", "react-colorful": "^5.6.1", "react-day-picker": "^9.8.1", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.4", "react-textarea-autosize": "^8.5.9", "recharts": "^3.1.0", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "resend": "^4.7.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwind-scrollbar-hide": "^4.0.0", "tailwind-variants": "^2.1.0", "tailwindcss": "4.1.11", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^2.0.4", "uuid": "^11.1.0", "zod": "^3.25.76"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "@workspace/typescript-config": "workspace:*", "typescript": "^5.9.2"}}