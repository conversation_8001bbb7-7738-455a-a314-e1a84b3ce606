# Design Document

## Overview

This design outlines the migration of waitlist functionality from Prisma server actions with TanStack Query to Convex real-time queries and mutations. The migration will maintain all existing functionality while enabling real-time updates across all connected clients. The design focuses on preserving the current UI components and data structures while replacing the data layer.

## Architecture

### Current Architecture
- **Server Actions**: Prisma-based server actions in `apps/web/actions/waitlist/`
- **Client State**: TanStack Query for client-side caching and state management
- **Data Fetching**: Server-side prefetching with hydration boundaries
- **Real-time**: No real-time capabilities

### Target Architecture
- **Convex Functions**: Real-time queries, mutations, and actions in `packages/backend/convex/projects/waitlist.ts`
- **Client State**: Convex React hooks for automatic real-time updates
- **Data Fetching**: Client-side only with Convex hooks
- **Real-time**: Automatic real-time updates across all connected clients

## Components and Interfaces

### Convex Functions to Implement

#### Queries
```typescript
// Get all waitlists for organization with stats
export const getAllWaitlists = query({
  args: {},
  handler: async (ctx) => {
    // Returns waitlists with embedded stats
  }
});

// Get single waitlist with integration details
export const getWaitlist = query({
  args: { id: v.id("Waitlist") },
  handler: async (ctx, { id }) => {
    // Returns waitlist with email sync integration info
  }
});

// Get waitlist analytics
export const getWaitlistAnalytics = query({
  args: { waitlistId: v.id("Waitlist") },
  handler: async (ctx, { waitlistId }) => {
    // Returns comprehensive analytics data
  }
});

// Get waitlist entries with filtering
export const getWaitlistEntries = query({
  args: {
    waitlistId: v.id("Waitlist"),
    search: v.optional(v.string()),
    status: v.optional(v.string()),
    limit: v.optional(v.number()),
    offset: v.optional(v.number())
  },
  handler: async (ctx, args) => {
    // Returns filtered and paginated entries
  }
});
```

#### Mutations
```typescript
// Create waitlist
export const createWaitlist = mutation({
  args: {
    projectId: v.id("Project"),
    name: v.string(),
    slug: v.string(),
    description: v.string(),
    isPublic: v.boolean(),
    allowNameCapture: v.boolean(),
    showPosition: v.boolean(),
    showSocialProof: v.boolean(),
    customMessage: v.optional(v.string()),
    emailSyncEnabled: v.optional(v.boolean()),
    integrationId: v.optional(v.id("Integration"))
  },
  handler: async (ctx, args) => {
    // Creates waitlist and integration usage if needed
  }
});

// Update waitlist
export const updateWaitlist = mutation({
  args: {
    id: v.id("Waitlist"),
    // ... update fields
  },
  handler: async (ctx, args) => {
    // Updates waitlist and manages integration usage
  }
});

// Delete waitlist
export const deleteWaitlist = mutation({
  args: { id: v.id("Waitlist") },
  handler: async (ctx, { id }) => {
    // Deletes waitlist and related data
  }
});

// Entry management mutations
export const updateEntryStatus = mutation({
  args: {
    entryId: v.id("WaitlistEntry"),
    status: v.string()
  },
  handler: async (ctx, args) => {
    // Updates entry status with timestamps
  }
});

export const deleteEntry = mutation({
  args: { entryId: v.id("WaitlistEntry") },
  handler: async (ctx, { entryId }) => {
    // Deletes waitlist entry
  }
});
```

#### Actions
```typescript
// Check slug availability
export const checkSlugAvailability = action({
  args: {
    slug: v.string(),
    excludeId: v.optional(v.id("Waitlist"))
  },
  handler: async (ctx, args) => {
    // Checks if slug is available
  }
});
```

### Component Migration Strategy

#### Page Components (Server → Client)
```typescript
// Before: apps/web/app/(dashboard)/waitlist/page.tsx
export default async function WaitlistPage() {
  // Remove server-side data fetching
  // Remove queryClient.prefetchQuery
  return <WaitlistClient />;
}

// After: Pure server component that only renders client component
export default function WaitlistPage() {
  return (
    <div>
      <Header crumb={[{ title: "Waitlists", url: "/waitlist" }]}>
        <Link href="/waitlist/new">
          <Button size="sm" variant="fancy">
            <Plus className="w-4 h-4 mr-2" /> New Waitlist
          </Button>
        </Link>
      </Header>
      <WaitlistClient />
    </div>
  );
}
```

#### Client Components (TanStack → Convex)
```typescript
// Before: apps/web/app/(dashboard)/waitlist/waitlist-client.tsx
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getAllWaitlists, deleteWaitlist } from "@/actions/waitlist";

const { data: waitlistsResponse, isLoading } = useQuery({
  queryKey: ["waitlists", org],
  queryFn: () => getAllWaitlists(),
});

// After: Using Convex hooks
import { useQuery, useMutation } from "convex/react";
import { api } from "@workspace/backend/convex/_generated/api";

const waitlists = useQuery(api.projects.waitlist.getAllWaitlists);
const deleteWaitlistMutation = useMutation(api.projects.waitlist.deleteWaitlist);
```

## Data Models

### Existing Convex Schema (Preserved)
The current Convex schema will be maintained to ensure UI compatibility:

```typescript
// Waitlist table
{
  _id: Id<"Waitlist">,
  name: string,
  slug: string,
  description: string,
  isPublic: boolean,
  allowNameCapture: boolean,
  showPosition: boolean,
  showSocialProof: boolean,
  customMessage?: string,
  organizationId: Id<"Organization">,
  projectId: Id<"Project">,
  createdById: Id<"User">,
  _creationTime: number
}

// WaitlistEntry table
{
  _id: Id<"WaitlistEntry">,
  waitlistId: Id<"Waitlist">,
  email: string,
  name: string,
  status: string,
  position: number,
  referralCode: string,
  ipAddress: string,
  utmSource?: string,
  utmMedium?: string,
  utmCampaign?: string,
  _creationTime: number
}
```

### Data Transformation Layer
Functions will transform Convex data to match the format expected by existing UI components:

```typescript
// Transform Convex waitlist to match Prisma format
const transformWaitlistForUI = (waitlist: Doc<"Waitlist">, stats: any) => ({
  id: waitlist._id,
  name: waitlist.name,
  slug: waitlist.slug,
  description: waitlist.description,
  isPublic: waitlist.isPublic,
  allowNameCapture: waitlist.allowNameCapture,
  showPosition: waitlist.showPosition,
  showSocialProof: waitlist.showSocialProof,
  customMessage: waitlist.customMessage,
  organizationId: waitlist.organizationId,
  projectId: waitlist.projectId,
  createdById: waitlist.createdById,
  createdAt: new Date(waitlist._creationTime).toISOString(),
  stats
});
```

## Error Handling

### Convex Error Patterns
```typescript
// Query error handling
const waitlists = useQuery(api.projects.waitlist.getAllWaitlists);

if (waitlists === undefined) {
  return <LoadingSpinner />;
}

if (waitlists === null) {
  return <NoData />;
}

// Mutation error handling
const deleteWaitlistMutation = useMutation(api.projects.waitlist.deleteWaitlist);

const handleDelete = async (id: string) => {
  try {
    await deleteWaitlistMutation({ id });
    toast.success("Waitlist deleted successfully");
  } catch (error) {
    toast.error("Failed to delete waitlist");
  }
};
```

### Authentication Integration
All Convex functions will use the existing Better Auth integration:

```typescript
import { betterAuth } from "../auth";

export const getAllWaitlists = query({
  args: {},
  handler: async (ctx) => {
    const identity = await betterAuth.getAuthUser(ctx);
    if (!identity) {
      throw new Error("Unauthorized");
    }
    
    const org = identity.organizationId;
    // Query waitlists for organization
  }
});
```

## Testing Strategy

### Component Testing
1. **Unit Tests**: Test individual Convex functions with mock data
2. **Integration Tests**: Test component behavior with Convex hooks
3. **Real-time Tests**: Verify real-time updates work across multiple clients

### Migration Testing
1. **Data Consistency**: Ensure Convex functions return data in expected format
2. **UI Compatibility**: Verify all existing UI components work without changes
3. **Performance**: Compare query performance between Prisma and Convex
4. **Real-time**: Test real-time updates across different scenarios

### Test Cases
```typescript
// Example test for waitlist query
describe("getAllWaitlists", () => {
  it("should return waitlists with stats in expected format", async () => {
    const result = await getAllWaitlists(mockCtx);
    expect(result).toHaveProperty("id");
    expect(result).toHaveProperty("stats.totalEntries");
    expect(result.createdAt).toMatch(ISO_DATE_REGEX);
  });
});
```

## Performance Considerations

### Query Optimization
- Add indexes for frequently queried fields
- Use compound indexes for complex queries
- Implement pagination for large datasets

### Real-time Efficiency
- Minimize unnecessary re-renders with proper dependency arrays
- Use Convex's built-in optimizations for real-time updates
- Cache computed values where appropriate

### Migration Strategy
1. **Phase 1**: Implement Convex functions alongside existing server actions
2. **Phase 2**: Update components one by one to use Convex hooks
3. **Phase 3**: Remove server actions and TanStack Query dependencies
4. **Phase 4**: Add performance optimizations and indexes