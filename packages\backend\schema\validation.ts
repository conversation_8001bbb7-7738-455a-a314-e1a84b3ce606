import { defineTable } from "convex/server";
import { v } from "convex/values";
import {
  ValidationStatus,
  ValidationScore,
  MarketSize,
  MarketGrowthRate,
  MarketMaturity,
  CompetitionLevel,
  CustomerSegment,
  RevenueModel,
  PricingStrategy,
  RiskLevel,
  RiskCategory,
  FundingStage,
  InvestmentRecommendation,
  GoToMarketStrategy,
  ValidationMethod,
  FinancialMetric,
} from "./enum";

// Main validation container for an idea
export const ideaValidation = defineTable({
  ideaId: v.id("ideas"),
  
  // Overall validation metrics (chart-friendly)
  overallScore: v.number(), // 0-100
  overallStatus: ValidationStatus,
  confidenceLevel: v.number(), // 0-100
  validationProgress: v.number(), // 0-100 percentage complete

  // Validation timeline
  startedAt: v.number(),
  completedAt: v.optional(v.number()),
  lastUpdatedAt: v.number(),

  // Revalidation support fields
  version: v.number(), // Version number for tracking changes
  parentValidationId: v.optional(v.id("ideaValidation")), // Reference to previous validation version
  isLatest: v.boolean(), // Is this the latest version
  revalidationReason: v.optional(v.string()), // Why revalidation was triggered
  dataSourcesUpdated: v.array(v.string()), // Which data sources were updated
  lastDataRefresh: v.optional(v.number()), // When external data was last refreshed
  nextRevalidationDue: v.optional(v.number()), // When next revalidation is recommended
});

// Consolidated metrics for better chart visualization
export const validationMetrics = defineTable({
  validationId: v.id("ideaValidation"),

  // Core strength score (0-100) - consolidated from 6 individual scores
  overallStrengthScore: v.number(),

  // Core risk score (0-100) - consolidated from 6 individual risk scores  
  overallRiskScore: v.number(),

  // Key performance indicators
  timeToMarket: v.optional(v.number()), // Months
  fundingRequired: v.optional(v.number()), // USD
  breakEvenMonth: v.optional(v.number()), // Months
  customerPayback: v.optional(v.number()), // Months
  marketPenetration: v.optional(v.number()), // Percentage

  // Action priority counts
  immediateActions: v.number(),
  shortTermActions: v.number(),
  longTermActions: v.number(),
});

// Market validation with chart-optimized structure
export const marketValidation = defineTable({
  validationId: v.id("ideaValidation"),

  // Market Size Metrics (chart-friendly numbers)
  totalAddressableMarket: v.optional(v.number()), // TAM in millions USD
  serviceableAddressableMarket: v.optional(v.number()), // SAM in millions USD  
  serviceableObtainableMarket: v.optional(v.number()), // SOM in millions USD
  marketGrowthRate: v.optional(v.number()), // Annual growth percentage

  // Customer Metrics
  primaryCustomerSegment: CustomerSegment,
  customerInterviews: v.number(),
  surveyResponses: v.number(),

  // Core validation scores (0-100) - consolidated from multiple individual scores
  overallMarketScore: v.number(),

  // Geographic distribution
  primaryRegions: v.array(v.string()), // Array of region codes

  // Validation status
  status: ValidationStatus,
});

// Simplified insights table for market data
export const marketInsight = defineTable({
  marketValidationId: v.id("marketValidation"),

  category: v.string(), // "trend", "opportunity", "threat", "pain_point", "success_factor"
  impact: v.number(), // 0-100 impact score
  urgency: v.number(), // 0-100 urgency score

  // Optional details (only when needed for UI)
  label: v.optional(v.string()),
  description: v.optional(v.string()),
});

// Market region scores for geographic visualization
export const marketRegionScore = defineTable({
  marketValidationId: v.id("marketValidation"),

  region: v.string(), // Region code (e.g., "US", "EU", "APAC")
  score: v.number(), // 0-100 market opportunity score for this region
});

// Business validation with chart-optimized financial metrics
export const businessValidation = defineTable({
  validationId: v.id("ideaValidation"),

  // Revenue Model (simplified)
  primaryRevenueModel: RevenueModel,
  pricingStrategy: PricingStrategy,
  pricePoint: v.optional(v.number()), // Primary price in USD

  // Core Unit Economics (essential metrics only)
  customerAcquisitionCost: v.optional(v.number()), // CAC in USD
  customerLifetimeValue: v.optional(v.number()), // LTV in USD
  monthlyChurnRate: v.optional(v.number()), // Monthly churn percentage

  // Key Financial Metrics
  breakEvenMonth: v.optional(v.number()), // Month when revenue >= costs
  initialInvestment: v.optional(v.number()), // Required startup capital
  totalFundingNeeded: v.optional(v.number()), // Total capital required

  // Go-to-Market Metrics
  goToMarketStrategy: GoToMarketStrategy,
  salesCycleLength: v.optional(v.number()), // Days

  // Core validation scores (0-100) - consolidated from multiple individual scores
  overallBusinessScore: v.number(),

  // Validation status
  status: ValidationStatus,
});

// Simplified insights table for business data
export const businessInsight = defineTable({
  businessValidationId: v.id("businessValidation"),

  category: v.string(), // "risk", "opportunity", "strategy", "metric"
  impact: v.number(), // 0-100 impact score
  urgency: v.number(), // 0-100 urgency score
  cost: v.optional(v.number()), // Associated cost in USD (if applicable)

  // Optional details (only when needed for UI)
  label: v.optional(v.string()),
  description: v.optional(v.string()),
});

// Risk analysis model
export const riskAnalysis = defineTable({
  validationId: v.id("ideaValidation"),

  overallRiskScore: v.number(), // 0-100, calculated from items
  status: ValidationStatus,
});

// Individual risk item
export const riskItem = defineTable({
  riskAnalysisId: v.id("riskAnalysis"),

  category: RiskCategory,
  description: v.string(),
  impact: v.number(), // 1-5 (Low to High)
  probability: v.number(), // 1-5 (Low to High)
  mitigation: v.string(), // Mitigation plan
});

// Product-Market Fit analysis model
export const productMarketFitAnalysis = defineTable({
  validationId: v.id("ideaValidation"),

  pmfScore: v.number(), // 0-100
  status: ValidationStatus,
});

// Metrics for Product-Market Fit
export const pmfMetric = defineTable({
  productMarketFitAnalysisId: v.id("productMarketFitAnalysis"),

  name: v.string(), // e.g., "NPS", "Churn Rate", "Activation Rate"
  value: v.number(),
  unit: v.string(), // e.g., "%", "score", "users"
  trend: v.optional(v.number()), // e.g., monthly change
  benchmark: v.optional(v.number()), // industry benchmark
});

// Qualitative feedback for Product-Market Fit
export const pmfFeedback = defineTable({
  productMarketFitAnalysisId: v.id("productMarketFitAnalysis"),

  source: v.string(), // e.g., "Survey", "User Interview"
  sentiment: v.string(), // "positive", "negative", "neutral"
  content: v.string(),
  tags: v.array(v.string()), // e.g., ["UI", "pricing", "feature_request"]
});

// Monthly financial projections for time-series charts
export const monthlyProjection = defineTable({
  businessValidationId: v.id("businessValidation"),

  month: v.number(), // Month number (1-36 for 3-year projection)
  revenue: v.number(), // Projected revenue for this month
  costs: v.number(), // Projected costs for this month
  users: v.number(), // Projected user count for this month
});

// Customer acquisition channels with effectiveness scores
export const acquisitionChannel = defineTable({
  businessValidationId: v.id("businessValidation"),

  channel: v.string(), // Channel name (e.g., "SEO", "Paid Ads", "Content Marketing")
  effectiveness: v.number(), // 0-100 effectiveness score
  cost: v.optional(v.number()), // Cost per acquisition for this channel
});

// Pricing tiers and competitor analysis for pricing strategy
export const pricingTier = defineTable({
  pricingStrategyAnalysisId: v.id("pricingStrategyAnalysis"),

  tierName: v.string(),
  tierPrice: v.number(), // Price in USD
  tierFeatures: v.array(v.string()), // Features included
  targetSegment: v.string(), // Target customer segment

  // Tier performance
  conversionRate: v.number(), // 0-100 expected conversion
  popularityScore: v.number(), // 0-100 expected popularity
  profitMargin: v.number(), // 0-100 profit margin
  competitiveScore: v.number(), // 0-100 competitive positioning
});

export const competitorPricing = defineTable({
  pricingStrategyAnalysisId: v.id("pricingStrategyAnalysis"),

  competitorName: v.string(),
  pricingModel: v.string(), // "subscription", "one-time", "usage-based", "freemium"
  basePrice: v.number(), // Base price in USD
  premiumPrice: v.optional(v.number()), // Premium tier price

  // Competitive analysis
  featureComparison: v.number(), // 0-100 feature comparison score
  valueComparison: v.number(), // 0-100 value comparison
  marketPosition: v.string(), // "premium", "value", "budget"

  // Market response
  marketShare: v.number(), // 0-100 estimated market share
  customerSatisfaction: v.number(), // 0-100 customer satisfaction
  pricingAdvantage: v.number(), // -100 to 100 pricing advantage vs us
});

// Customer Journey Mapping Research
export const customerJourneyMapping = defineTable({
  validationId: v.id("ideaValidation"),

  // Journey overview
  totalJourneyStages: v.number(),
  averageJourneyTime: v.number(), // Days

  // Core validation scores (0-100) - consolidated from multiple individual scores
  overallJourneyScore: v.number(),

  // Journey optimization metrics
  conversionRate: v.number(), // Overall journey conversion rate
  dropOffRate: v.number(), // Overall drop-off rate
  customerSatisfaction: v.number(), // 0-100 satisfaction score
});

export const journeyStage = defineTable({
  customerJourneyMappingId: v.id("customerJourneyMapping"),

  stageName: v.string(),
  stageOrder: v.number(), // Order in the journey
  averageDuration: v.optional(v.number()), // Average time spent in this stage (hours)

  // Stage metrics
  conversionRate: v.number(), // 0-100 conversion to next stage
  satisfactionScore: v.number(), // 0-100 satisfaction in this stage
  frictionScore: v.number(), // 0-100 friction experienced
  emotionalState: v.number(), // -100 to 100 emotional state

  // Stage characteristics
  customerGoals: v.array(v.string()), // What customer wants to achieve
  customerActions: v.array(v.string()), // Actions customer takes
  customerThoughts: v.array(v.string()), // Customer thoughts/concerns
  customerEmotions: v.array(v.string()), // Customer emotions

  // Business metrics
  dropOffRate: v.number(), // 0-100 drop-off rate from this stage
  supportTickets: v.number(), // Support tickets generated
  timeToComplete: v.number(), // Average time to complete stage
});

export const touchpoint = defineTable({
  customerJourneyMappingId: v.id("customerJourneyMapping"),

  touchpointName: v.string(),
  touchpointType: v.string(), // "digital", "physical", "human", "automated"
  channel: v.string(), // "website", "email", "phone", "store", etc.
  stageInJourney: v.string(), // Which journey stage this belongs to

  // Touchpoint performance
  effectivenessScore: v.number(), // 0-100 effectiveness
  satisfactionScore: v.number(), // 0-100 customer satisfaction
  usageFrequency: v.number(), // 0-100 how often used
  importanceScore: v.number(), // 0-100 importance to journey

  // Optimization metrics
  optimizationPotential: v.number(), // 0-100 optimization potential
  costEfficiency: v.number(), // 0-100 cost efficiency
  automationPotential: v.number(), // 0-100 automation potential

  // Context
  customerExpectations: v.array(v.string()), // What customers expect
  currentExperience: v.optional(v.string()), // Current experience description
  improvementAreas: v.array(v.string()), // Areas for improvement
});

export const journeyPainPoint = defineTable({
  customerJourneyMappingId: v.id("customerJourneyMapping"),

  painPointName: v.string(),
  journeyStage: v.string(), // Which stage this pain occurs
  painCategory: v.string(), // "process", "information", "emotional", "technical"

  // Pain metrics
  severityScore: v.number(), // 0-100 severity
  frequencyScore: v.number(), // 0-100 frequency of occurrence
  impactScore: v.number(), // 0-100 impact on journey
  resolutionDifficulty: v.number(), // 0-100 difficulty to resolve

  // Business impact
  dropOffIncrease: v.number(), // % increase in drop-off due to this pain
  supportCost: v.number(), // Cost of support for this pain
  revenueImpact: v.number(), // Revenue impact in USD

  // Resolution
  currentMitigation: v.optional(v.string()), // How currently handled
  proposedSolution: v.optional(v.string()), // Proposed solution
  solutionPriority: v.number(), // 0-100 priority for resolution
});

// Target Audience Segmentation Research
export const targetAudienceSegmentation = defineTable({
  validationId: v.id("ideaValidation"),

  // Segmentation overview
  primarySegment: v.string(),
  totalSegments: v.number(),
  totalMarketSize: v.number(), // Total addressable market size

  // Core validation scores (0-100) - consolidated from multiple individual scores
  overallSegmentationScore: v.number(),

  // Key metrics
  averageSegmentSize: v.number(), // Average segment size
  segmentAccessibility: v.number(), // 0-100 ease of reaching segments
  marketPenetration: v.number(), // Expected market penetration %
});

export const audienceSegment = defineTable({
  targetAudienceSegmentationId: v.id("targetAudienceSegmentation"),

  segmentName: v.string(),
  segmentSize: v.number(), // Percentage of total market
  attractivenessScore: v.number(), // 0-100 score
  accessibilityScore: v.number(), // 0-100 ease of reach
  profitabilityScore: v.number(), // 0-100 profit potential

  // Key characteristics
  primaryNeed: v.optional(v.string()),
  secondaryNeeds: v.array(v.string()),
  preferredSolution: v.optional(v.string()),
  budgetRange: v.optional(v.string()),
});

// Market Trend Analysis Research
export const marketTrendAnalysis = defineTable({
  validationId: v.id("ideaValidation"),

  // Trend analysis overview
  primaryTrend: v.string(),
  totalTrendsTracked: v.number(),
  analysisTimeframe: v.number(), // Months of trend analysis

  // Core validation scores (0-100) - consolidated from multiple individual scores
  overallTrendScore: v.number(),

  // Key trend metrics
  trendStrength: v.number(), // 0-100 strength of primary trend
  marketGrowthRate: v.number(), // Annual market growth percentage
  adoptionRate: v.number(), // Technology/trend adoption rate
});

export const marketTrend = defineTable({
  marketTrendAnalysisId: v.id("marketTrendAnalysis"),

  trendName: v.string(),
  trendCategory: v.string(), // "technology", "social", "economic", "regulatory"
  impactScore: v.number(), // 0-100 impact on business
  timelineMonths: v.optional(v.number()), // Expected timeline
  certaintyLevel: v.number(), // 0-100 certainty of trend

  // Opportunity/threat assessment
  opportunityScore: v.number(), // 0-100 opportunity potential
  threatScore: v.number(), // 0-100 threat level

  description: v.optional(v.string()),
});

// Customer Needs & Pain Points Research
export const customerNeedAnalysis = defineTable({
  validationId: v.id("ideaValidation"),

  // Analysis overview
  primaryNeed: v.string(),
  totalNeedsIdentified: v.number(),
  totalPainPoints: v.number(),

  // Core validation scores (0-100) - consolidated from multiple individual scores
  overallNeedScore: v.number(),

  // Key metrics
  needUrgency: v.number(), // 0-100 urgency of primary need
  solutionGap: v.number(), // 0-100 gap in current solutions
  customerWillingness: v.number(), // 0-100 willingness to pay for solution
});

export const customerNeed = defineTable({
  customerNeedAnalysisId: v.id("customerNeedAnalysis"),

  needName: v.string(),
  needCategory: v.string(), // "functional", "emotional", "social"
  intensityScore: v.number(), // 0-100 intensity
  frequencyScore: v.number(), // 0-100 frequency
  urgencyScore: v.number(), // 0-100 urgency
  satisfactionGap: v.number(), // 0-100 gap in current satisfaction

  // Context
  triggerEvents: v.array(v.string()), // What triggers this need
  desiredOutcome: v.optional(v.string()), // What success looks like
  currentSolution: v.optional(v.string()), // How they solve it now
});

export const painPoint = defineTable({
  customerNeedAnalysisId: v.id("customerNeedAnalysis"),

  painName: v.string(),
  painCategory: v.string(), // "process", "cost", "time", "quality", "experience"
  severityScore: v.number(), // 0-100 severity
  frequencyScore: v.number(), // 0-100 frequency
  impactScore: v.number(), // 0-100 business impact
  emotionalToll: v.number(), // 0-100 emotional impact

  // Cost analysis
  timeCostHours: v.optional(v.number()), // Hours lost per occurrence
  financialCost: v.optional(v.number()), // Dollar cost per occurrence
  opportunityCost: v.optional(v.number()), // Missed opportunities cost

  // Context
  painTriggers: v.array(v.string()), // What causes this pain
  currentMitigation: v.optional(v.string()), // How they currently handle it
});

// Pricing Strategy Analysis Research
export const pricingStrategyAnalysis = defineTable({
  validationId: v.id("ideaValidation"),

  // Pricing strategy overview
  primaryStrategy: PricingStrategy,
  recommendedPrice: v.optional(v.number()), // Primary recommended price point
  totalTiersAnalyzed: v.number(),

  // Core validation scores (0-100) - consolidated from multiple individual scores
  overallPricingScore: v.number(),

  // Key pricing metrics
  priceAcceptance: v.number(), // 0-100 customer price acceptance
  competitivenessScore: v.number(), // 0-100 competitive positioning
  profitabilityScore: v.number(), // 0-100 profitability potential
});