import { v } from "convex/values";
import { WaitlistEntryStatus } from "./enum";
import { defineTable } from "convex/server";
import {
  ProjectPlatform,
  ProjectStatus,
  IssueStatus,
  IssueLabel,
  Importance,
  AssetType,
  LinkType,
  AssetCategory,
  ActivityType,
  EntityType,
  RoadmapFeedbackSentiment,
  FeatureRequestStatus,
  FeatureRequestPriority,
  ChangelogEntryType,
  FeaturePhase,
  MilestoneStatus,
} from "./enum";

// Project schema
export const Project = defineTable({
  name: v.string(),
  description: v.optional(v.string()),
  platform: ProjectPlatform,
  ai: v.optional(v.string()),
  orm: v.optional(v.string()),
  database: v.optional(v.string()),
  auth: v.optional(v.string()),
  framework: v.optional(v.string()),
  infrastructure: v.optional(v.string()),
  dueDate: v.optional(v.number()),
  status: v.optional(ProjectStatus),
  ideaId: v.optional(v.id("Idea")),
  organizationId: v.optional(v.string()),
  createdById: v.optional(v.id("User")),
  _creationTime: v.number(),
})
  .index("by_organization", ["organizationId"])
  .index("by_idea", ["ideaId"])
  .index("by_status", ["status"]);

// Issue schema
export const Issue = defineTable({
  title: v.string(),
  description: v.optional(v.string()),
  organizationId: v.string(),
  projectId: v.id("Project"),
  milestoneId: v.optional(v.id("Milestone")),
  featureId: v.optional(v.id("Feature")),
  parentIssueId: v.optional(v.id("Issue")),
  status: IssueStatus,
  priority: Importance,
  label: IssueLabel,
  dueDate: v.optional(v.number()),
  assignedToId: v.optional(v.id("User")),
  achieved: v.optional(v.boolean()),
  isPublic: v.optional(v.boolean()),
  sourceType: v.optional(v.string()),
  sourceFeedbackId: v.optional(v.string()),
})
  .index("by_project", ["projectId"])
  .index("by_organization", ["organizationId"])
  .index("by_assignee", ["assignedToId"])
  .index("by_status", ["status"]);

// IssueDependency schema
export const IssueDependency = defineTable({
  organizationId: v.string(),
  issueId: v.id("Issue"),
  dependencyId: v.id("Issue"),
});

// IssueLink schema
export const IssueLink = defineTable({
  organizationId: v.string(),
  issueId: v.id("Issue"),
  url: v.string(),
});

// Asset schema
export const Asset = defineTable({
  name: v.string(),
  description: v.optional(v.string()),
  type: AssetType,
  projectId: v.id("Project"),
  organizationId: v.string(),
  storageId: v.optional(v.string()),
  fileName: v.optional(v.string()),
  fileSize: v.optional(v.number()),
  mimeType: v.optional(v.string()),
  url: v.optional(v.string()),
  linkType: v.optional(LinkType),
  tags: v.array(v.string()),
  category: v.optional(AssetCategory),
  thumbnailUrl: v.optional(v.string()),
  isPublic: v.optional(v.boolean()),
  uploadedById: v.optional(v.id("User")),
})
  .index("by_project", ["projectId"])
  .index("by_organization", ["organizationId"])
  .index("by_type", ["type"]);

// ActivityFeed schema
export const ActivityFeed = defineTable({
  type: ActivityType,
  title: v.string(),
  description: v.optional(v.string()),
  entityType: EntityType,
  entityId: v.string(),
  organizationId: v.string(),
  userId: v.optional(v.id("User")),
  oldValue: v.optional(v.string()),
  newValue: v.optional(v.string()),
});

// PublicRoadmap schema
export const PublicRoadmap = defineTable({
  projectId: v.id("Project"),
  name: v.string(),
  slug: v.string(),
  description: v.string(),
  isPublic: v.boolean(),
  allowVoting: v.boolean(),
  allowFeedback: v.boolean(),
})
  .index("bySlug", ["slug"])
  .index("byProject", ["projectId"])
  .searchIndex("searchSlug", {
    searchField: "slug",
  });

// RoadmapItem schema
export const RoadmapItem = defineTable({
  roadmapId: v.id("PublicRoadmap"),
  title: v.string(),
  description: v.string(),
  status: IssueStatus,
  category: IssueLabel,
  isPublic: v.boolean(),
  priority: Importance,
  targetDate: v.optional(v.number()),
}).index("by_roadmapId", ["roadmapId"]);

// RoadmapVote schema
export const RoadmapVote = defineTable({
  roadmapItemId: v.id("RoadmapItem"),
  userId: v.optional(v.string()),
  ipAddress: v.string(),
  createdAt: v.number(),
}).index("by_roadmapItemId", ["roadmapItemId"]);

// RoadmapFeedback schema
export const RoadmapFeedback = defineTable({
  roadmapItemId: v.id("RoadmapItem"),
  userId: v.optional(v.string()),
  ipAddress: v.string(),
  content: v.string(),
  sentiment: RoadmapFeedbackSentiment,
  isApproved: v.boolean(),
  convertedToFeatureId: v.optional(v.id("Feature")),
  convertedToIssueId: v.optional(v.id("Issue")),
  convertedAt: v.optional(v.number()),
  convertedBy: v.optional(v.string()),
  conversionNotes: v.optional(v.string()),
  createdAt: v.number(),
}).index("by_roadmapItemId", ["roadmapItemId"]);

// RoadmapChangelog schema
export const RoadmapChangelog = defineTable({
  roadmapId: v.id("PublicRoadmap"),
  title: v.string(),
  description: v.string(),
  version: v.optional(v.string()),
  publishDate: v.number(),
  isPublished: v.boolean(),
  fixes: v.array(v.string()),
  newFeatures: v.array(v.string()),
}).index("by_roadmapId", ["roadmapId"]);

// ChangelogEntry schema
export const ChangelogEntry = defineTable({
  changelogId: v.id("RoadmapChangelog"),
  type: ChangelogEntryType,
  title: v.string(),
  description: v.optional(v.string()),
  issueId: v.optional(v.id("Issue")),
  featureId: v.optional(v.id("Feature")),
  priority: v.optional(Importance),
  category: v.optional(v.string()),
  breaking: v.boolean(),
}).index("by_changelogId", ["changelogId"]);

// FeatureRequest schema
export const FeatureRequest = defineTable({
  roadmapId: v.id("PublicRoadmap"),
  title: v.string(),
  description: v.string(),
  category: v.string(),
  email: v.string(),
  name: v.optional(v.string()),
  ipAddress: v.string(),
  status: FeatureRequestStatus,
  priority: FeatureRequestPriority,
  isPublic: v.boolean(),
  adminNotes: v.optional(v.string()),
  convertedToFeatureId: v.optional(v.id("Feature")),
  convertedToIssueId: v.optional(v.id("Issue")),
  convertedToRoadmapItemId: v.optional(v.id("RoadmapItem")),
  convertedAt: v.optional(v.number()),
  convertedBy: v.optional(v.string()),
  conversionNotes: v.optional(v.string()),
}).index("by_roadmapId", ["roadmapId"]);

// Waitlist schema
export const Waitlist = defineTable({
  projectId: v.id("Project"),
  name: v.string(),
  slug: v.string(),
  description: v.string(),
  isPublic: v.boolean(),
  allowNameCapture: v.boolean(),
  showPosition: v.boolean(),
  showSocialProof: v.boolean(),
  customMessage: v.optional(v.string()),
  organizationId: v.string(),
  createdById: v.optional(v.id("User")),
}).index("by_slug", ["slug"]);

// WaitlistEntry schema
export const WaitlistEntry = defineTable({
  waitlistId: v.id("Waitlist"),
  email: v.string(),
  name: v.optional(v.string()),
  status: WaitlistEntryStatus,
  position: v.number(),
  referralCode: v.string(),
  referredBy: v.optional(v.string()),
  verificationToken: v.optional(v.string()),
  verifiedAt: v.optional(v.number()),
  invitedAt: v.optional(v.number()),
  joinedAt: v.optional(v.number()),
  ipAddress: v.string(),
  userAgent: v.optional(v.string()),
  utmSource: v.optional(v.string()),
  utmMedium: v.optional(v.string()),
  utmCampaign: v.optional(v.string()),
})
  .index("by_waitlist", ["waitlistId"])
  .index("by_waitlist_email", ["waitlistId", "email"])
  .index("by_referral_code", ["waitlistId", "referralCode"]);

// Feature schema
export const Feature = defineTable({
  name: v.string(),
  description: v.string(),
  projectId: v.id("Project"),
  phase: FeaturePhase,
  businessValue: v.optional(v.number()),
  estimatedEffort: v.optional(v.number()),
  startDate: v.optional(v.number()),
  endDate: v.optional(v.number()),
  priority: Importance,
  assignedToId: v.optional(v.id("User")),
  parentFeatureId: v.optional(v.id("Feature")),
  organizationId: v.string(),
  milestoneId: v.optional(v.id("Milestone")),
})
  .index("by_project", ["projectId"])
  .index("by_organization", ["organizationId"])
  .index("by_assignee", ["assignedToId"])
  .index("by_phase", ["phase"]);

// FeatureDependency schema
export const FeatureDependency = defineTable({
  organizationId: v.string(),
  featureId: v.id("Feature"),
  dependencyId: v.id("Feature"),
});

// FeatureLink schema
export const FeatureLink = defineTable({
  organizationId: v.string(),
  featureId: v.id("Feature"),
  url: v.string(),
  title: v.optional(v.string()),
  description: v.optional(v.string()),
  image: v.optional(v.string()),
  siteName: v.optional(v.string()),
  favicon: v.optional(v.string()),
});

// Milestone schema
export const Milestone = defineTable({
  name: v.string(),
  description: v.optional(v.string()),
  status: MilestoneStatus,
  startDate: v.optional(v.number()),
  endDate: v.optional(v.number()),
  projectId: v.id("Project"),
  organizationId: v.string(),
  ownerId: v.optional(v.id("User")),
})
  .index("by_project", ["projectId"])
  .index("by_organization", ["organizationId"])
  .index("by_status", ["status"]);

// MilestoneDependency schema
export const MilestoneDependency = defineTable({
  organizationId: v.string(),
  milestoneId: v.id("Milestone"),
  dependencyId: v.id("Milestone"),
});

// Referral schema
export const Referral = defineTable({
  referrerId: v.id("WaitlistEntry"),
  referredEmail: v.string(),
  referredName: v.optional(v.string()),
  ipAddress: v.string(),
  userAgent: v.optional(v.string()),
  referrerCode: v.string(),
  waitlistId: v.id("Waitlist"),
  organizationId: v.string(),
})
  .index("by_referrer", ["referrerId"])
  .index("by_waitlist", ["waitlistId"]);

// AssetView schema
export const AssetView = defineTable({
  assetId: v.id("Asset"),
  organizationId: v.string(),
  userId: v.optional(v.id("User")),
  ipAddress: v.string(),
  userAgent: v.optional(v.string()),
  referrer: v.optional(v.string()),
  viewedAt: v.number(),
});

// AssetDownload schema
export const AssetDownload = defineTable({
  assetId: v.id("Asset"),
  organizationId: v.string(),
  userId: v.optional(v.id("User")),
  ipAddress: v.string(),
  userAgent: v.optional(v.string()),
  downloadedAt: v.number(),
});
