# Requirements Document

## Introduction

This feature involves migrating the waitlist functionality from Prisma server actions with TanStack Query to Convex real-time queries and mutations. The goal is to enable real-time updates across all waitlist components while maintaining the existing UI and functionality. This migration will improve user experience by providing live data updates without manual refreshes.

## Requirements

### Requirement 1

**User Story:** As a user managing waitlists, I want real-time updates of waitlist data so that I can see changes immediately without refreshing the page.

#### Acceptance Criteria

1. WHEN a waitlist entry is added THEN all connected clients SHALL see the updated count immediately
2. WHEN a waitlist entry status changes THEN all connected clients SHALL see the status update in real-time
3. WHEN a waitlist is deleted THEN all connected clients SHALL see it removed from the list immediately
4. WHEN waitlist analytics change THEN dashboard views SHALL update automatically

### Requirement 2

**User Story:** As a developer, I want to replace all Prisma server actions with Convex functions so that the application uses a consistent real-time backend.

#### Acceptance Criteria

1. WHEN implementing queries THEN the system SHALL use useQuery from convex/react instead of @tanstack/react-query
2. WHEN implementing mutations THEN the system SHALL use useMutation from convex/react instead of server actions
3. WH<PERSON> implementing actions THEN the system SHALL use useAction from convex/react for complex operations
4. WHEN removing dependencies THEN all @tanstack/react-query imports SHALL be removed from waitlist components

### Requirement 3

**User Story:** As a user, I want server-rendered pages to only render client components so that the application works with real-time data.

#### Acceptance Criteria

1. WHEN a page loads THEN server components SHALL NOT fetch data directly
2. WHEN a page renders THEN server components SHALL only render client components that handle data fetching
3. WHEN data is needed THEN client components SHALL use Convex hooks for data fetching
4. WHEN prefetching is removed THEN pages SHALL load faster and use real-time data

### Requirement 4

**User Story:** As a developer, I want to preserve the existing Convex schema so that the UI components continue to work without breaking changes.

#### Acceptance Criteria

1. WHEN migrating functions THEN the Convex schema SHALL remain unchanged unless adding indexes
2. WHEN adding indexes THEN they SHALL only improve query performance
3. WHEN implementing functions THEN they SHALL match the data structure expected by existing UI components
4. WHEN testing THEN all existing UI functionality SHALL work without modifications

### Requirement 5

**User Story:** As a user, I want all waitlist CRUD operations to work in real-time so that I can manage waitlists efficiently.

#### Acceptance Criteria

1. WHEN creating a waitlist THEN it SHALL appear in the list immediately for all users
2. WHEN updating a waitlist THEN changes SHALL be reflected immediately across all views
3. WHEN deleting a waitlist THEN it SHALL be removed immediately from all connected clients
4. WHEN managing entries THEN status changes SHALL update in real-time

### Requirement 6

**User Story:** As a user, I want waitlist analytics to update in real-time so that I can monitor performance continuously.

#### Acceptance Criteria

1. WHEN entries are added THEN analytics counters SHALL update immediately
2. WHEN entry statuses change THEN conversion rates SHALL recalculate automatically
3. WHEN referrals are made THEN referral statistics SHALL update in real-time
4. WHEN viewing analytics THEN data SHALL always be current without manual refresh