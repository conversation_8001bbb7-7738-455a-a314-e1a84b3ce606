import { v } from "convex/values";
import { defineTable } from "convex/server";
import { IntegrationType, ApiPermission } from "./enum";

// Integration schema
export const Integration = defineTable({
  name: v.string(),
  type: IntegrationType,
  config: v.any(), // JSON data
  isActive: v.optional(v.boolean()),
  organizationId: v.id("organizations"),
  createdById: v.optional(v.id("users")),
});

// IntegrationUsage schema
export const IntegrationUsage = defineTable({
  integrationId: v.id("integrations"),
  entityType: v.string(), // e.g., "waitlist", "project", "organization"
  entityId: v.string(), // ID of the entity (waitlistId, projectId, etc.)
  purpose: v.string(), // e.g., "email_sync", "analytics", "webhook", etc.
  isActive: v.optional(v.boolean()),
});

// ApiKey schema
export const ApiKey = defineTable({
  organizationId: v.id("organizations"),
  name: v.string(),
  keyHash: v.string(),
  keyPreview: v.string(),
  permissions: v.array(ApiPermission),
  createdBy: v.string(),
  createdAt: v.number(),
  lastUsed: v.optional(v.number()),
  isActive: v.boolean(),
  expiresAt: v.optional(v.number()),
});

// ApiCall schema
export const ApiCall = defineTable({
  organizationId: v.id("organizations"),
  apiKeyId: v.optional(v.id("apiKeys")),
  endpoint: v.string(),
  method: v.string(),
  statusCode: v.number(),
  responseTime: v.optional(v.number()), // in milliseconds
  userAgent: v.optional(v.string()),
  ipAddress: v.optional(v.string()),
});
